import PdfViewer from "@/modules/pdf/components/pdf";
import { IPdfOverlay } from "@/modules/pdf/types/pdf-overlay.interface";
import { isRendererRegisteredAtom, registerRenderer<PERSON>tom } from "@/modules/pdf/states";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import React, { useCallback, useMemo, useEffect } from "react";
import { usePdfWithOverlays } from "../../../pdf/hooks/overlay/use-pdf-with-overlays.hook";
import { useAutoSignatureScroll } from "../../hooks/pdf-integration/use-signature-scroll.hook";
import { SignatureOverlayRenderer } from "../../lib/pdf-integration/signature-overlay-renderer";
import { mainRequisitToSignAtom } from "../../states/signature/main-requisit-to-sign.state";
import { rubricSvgString } from "../../states/rubric/rubric-svg.state";
import { signaturePosition<PERSON>tom } from "../../states/signature/signature-position.state";
import { ISignatureOverlay, ISignatureRenderData } from "../../types/signature/signature-overlay.interface";

interface PdfWithSignatureProps {
	id: string;
	buffer: ArrayBuffer;
	isModal?: boolean;
	showSignatureAsPreview?: boolean;
}

/**
 * Componente que integra PDF com funcionalidades de assinatura
 * Responsabilidade única: coordenar PDF e assinatura
 * Segue o princípio da composição
 */
const PdfWithSignature: React.FC<PdfWithSignatureProps> = ({ id, buffer, isModal, showSignatureAsPreview = true }) => {
	const signaturePosition = useAtomValue(signaturePositionAtom);
	const rubricSvg = useAtomValue(rubricSvgString);
	const setMainRequisitToSign = useSetAtom(mainRequisitToSignAtom);
	// const isSignatureRendererRegistered = useAtomValue(isRendererRegisteredAtom("signature"));
	const registerRenderer = useSetAtom(registerRendererAtom);

	useAutoSignatureScroll(1, signaturePosition, rubricSvg);
	const { overlayManager } = usePdfWithOverlays();

	// // Registra o renderizador de assinatura globalmente apenas uma vez
	// useEffect(() => {
	// 	if (!isSignatureRendererRegistered) {
	// 		console.log("🔧 Registrando renderizador de assinatura globalmente");
	// 		const renderer = new SignatureOverlayRenderer();
	// 		const wasRegistered = registerRenderer(renderer);
	// 		if (wasRegistered) {
	// 			// Também registra no overlay manager local
	// 			overlayManager.registerRenderer(renderer);
	// 			console.log("🔧 Renderizador registrado com sucesso");
	// 		}
	// 	}
	// }, [isSignatureRendererRegistered, registerRenderer, overlayManager]);

	// Converte posição de assinatura para overlay
	const signatureOverlays = useMemo((): IPdfOverlay[] => {
		console.log("🔍 Debug - signaturePosition:", signaturePosition);
		console.log("🔍 Debug - rubricSvg:", rubricSvg ? "SVG presente" : "SVG ausente");

		if (!signaturePosition || !rubricSvg) {
			console.log("❌ Não criando overlay - faltam dados");
			return [];
		}

		const signatureOverlay: ISignatureOverlay = {
			id: "current-signature",
			type: "signature",
			page: signaturePosition.page,
			x: signaturePosition.x,
			y: signaturePosition.y,
			scale: signaturePosition.scale,
			svgData: rubricSvg,
			isPreview: showSignatureAsPreview,
		};

		return [signatureOverlay];
	}, [signaturePosition, rubricSvg, showSignatureAsPreview]);

	const overlayData = useMemo(
		(): Record<string, ISignatureRenderData> => ({
			signature: {
				svg: rubricSvg || "",
				showPreview: showSignatureAsPreview,
				previewText: "PRÉVIA",
				previewBorderColor: "#ff7e5f",
			},
		}),
		[rubricSvg, showSignatureAsPreview]
	);

	const handleDownloadRequested = useCallback(() => {
		setMainRequisitToSign(true);
	}, [setMainRequisitToSign]);

	return (
		<PdfViewer
			id={id}
			buffer={buffer}
			isModal={isModal}
			overlays={signatureOverlays}
			overlayData={overlayData}
			// onOverlayClick={handleOverlayClick}
			onDownloadRequested={handleDownloadRequested}
		/>
	);
};

export default PdfWithSignature;
