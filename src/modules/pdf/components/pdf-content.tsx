import { PDFDocumentProxy } from "pdfjs-dist";
import React, { memo, useMemo } from "react";
import { IPdfOverlay } from "../types/pdf-overlay.interface";
import PdfCanvas from "./pdf-canvas";

interface PdfViewerContentProps {
	pdfDocument: PDFDocumentProxy;
	totalPages: number;
	zoom: number;
	onRenderComplete: (pageNumber: number) => void;
	forceRenderAllPages: boolean;
	overlays?: IPdfOverlay[];
	overlayData?: Record<string, unknown>;
	onRenderOverlays?: (context: CanvasRenderingContext2D, page: number, scale: number, overlayData?: Record<string, unknown>) => Promise<void>;
}

const PdfViewerContent: React.FC<PdfViewerContentProps> = memo(
	({ pdfDocument, totalPages, zoom, onRenderComplete, forceRenderAllPages, overlays, overlayData, onRenderOverlays }) => {
		const pageNumbers = useMemo(() => Array.from({ length: totalPages }, (_, index) => index + 1), [totalPages]);

		return (
			<>
				{pageNumbers.map(pageNumber => (
					<PdfCanvas
						key={`pdf-page-${pageNumber}`}
						pdfDocument={pdfDocument}
						pageNumber={pageNumber}
						zoom={zoom}
						onRenderComplete={() => onRenderComplete(pageNumber)}
						forceRenderAllPages={forceRenderAllPages}
						overlays={overlays}
						overlayData={overlayData}
						onRenderOverlays={onRenderOverlays}
					/>
				))}
			</>
		);
	}
);

PdfViewerContent.displayName = "PdfViewerContent";

export default PdfViewerContent;
